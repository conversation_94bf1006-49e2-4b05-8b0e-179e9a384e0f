"use client";
import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import Navbar from "@/common/navbar/navbar.common";
import Sidebar from "@/common/sidebar/sidebar.common";
import MobileNavbar from "@/common/navbar/mobile-navbar.common";

const PosterPage = () => {
  const canvasRef = useRef(null);

  useEffect(() => {
    // Load external dependencies
    const loadDependencies = async () => {
      // Load CSS dependencies
      const cssLinks = [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
        'https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
      ];

      cssLinks.forEach(href => {
        if (!document.querySelector(`link[href="${href}"]`)) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          document.head.appendChild(link);
        }
      });

      // Load JS dependencies
      const scripts = [
        'https://code.jquery.com/jquery-3.6.0.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js',
        'https://html2canvas.hertzen.com/dist/html2canvas.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js',
        'https://cdn.jsdelivr.net/npm/spectrum-colorpicker2/dist/spectrum.min.js'
      ];

      for (const src of scripts) {
        if (!document.querySelector(`script[src="${src}"]`)) {
          await new Promise((resolve) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            document.head.appendChild(script);
          });
        }
      }

      // Initialize poster after all dependencies are loaded
      setTimeout(initializePoster, 500);
    };

    loadDependencies();

    return () => {
      // Cleanup
      try {
        if (window.canvas && typeof window.canvas.dispose === 'function') {
          window.canvas.dispose();
        } else if (window.canvas && typeof window.canvas.clear === 'function') {
          // Fallback to clear if dispose doesn't exist
          window.canvas.clear();
        }

        // Clean up global references
        if (window.canvas) {
          window.canvas = null;
        }
        if (window.backgroundImage) {
          window.backgroundImage = null;
        }
        if (window.styleLayer) {
          window.styleLayer = null;
        }
        if (window.cropper) {
          window.cropper.destroy();
          window.cropper = null;
        }
      } catch (error) {
        console.warn('Error during canvas cleanup:', error);
      }
    };
  }, []);

  const initializePoster = () => {
    if (typeof window !== 'undefined' && window.fabric && canvasRef.current) {
      // Initialize canvas using the original poster.js logic (reduced by 20%)
      const canvasWidth = Math.floor(648 * 0.8);
      const canvasHeight = Math.floor(900 * 0.8);
      
      window.canvas = new window.fabric.Canvas(canvasRef.current, {
        width: canvasWidth,
        height: canvasHeight,
        preserveObjectStacking: true,
        selection: true,
        selectionColor: 'rgba(100, 100, 255, 0.3)',
        selectionLineWidth: 2
      });

      // Set up functionality from original design
      setupDeleteFunctionality();
      setupLayerManagement();
      setupSelectionHandlers();
      
      // Load background and style layer
      loadBackgroundImage('/poster-images/10-4.jpg');
      setTimeout(() => {
        loadStyleLayer('/poster-images/Poster2-StyleLayer.png');
        initializeTextElements();
      }, 100);

      // Set up all the control handlers
      setupControlHandlers();
    }
  };

  const setupDeleteFunctionality = () => {
    document.addEventListener('keyup', function(e) {
      if ((e.key === 'Delete' || e.key === 'Backspace') && window.canvas?.getActiveObject()) {
        deleteSelectedObjects();
      }
    });
  };

  const deleteSelectedObjects = () => {
    if (!window.canvas) return;

    const activeObject = window.canvas.getActiveObject();
    if (!activeObject) return;

    try {
      if (activeObject.type === 'activeSelection') {
        const activeGroup = activeObject;
        window.canvas.discardActiveObject();
        activeGroup.getObjects().forEach(function(obj) {
          window.canvas.remove(obj);
        });
      } else {
        window.canvas.remove(activeObject);
      }
      window.canvas.requestRenderAll();
    } catch (error) {
      console.error('Error deleting objects:', error);
    }
  };

  const loadBackgroundImage = (url) => {
    window.fabric.Image.fromURL(url, function(img) {
      img.scaleToWidth(window.canvas.width);
      if (img.height * img.scaleY > window.canvas.height * 0.56) {
        img.scaleToHeight(window.canvas.height * 0.56);
      }
      
      img.set({
        left: 0,
        top: 0,
        selectable: true,
        hasControls: true,
        hasBorders: true,
        name: 'backgroundImage',
        excludeFromExport: false
      });
      
      window.backgroundImage = img;
      window.canvas.add(img);
      window.canvas.sendToBack(img);
      window.canvas.requestRenderAll();
    });
  };

  const loadStyleLayer = (url) => {
    console.log('Loading style layer from:', url);
    window.fabric.Image.fromURL(url, function(img) {
      console.log('Style layer loaded successfully');
      img.set({
        left: 0,
        top: 0,
        selectable: false,
        hasControls: false,
        hasBorders: false,
        lockMovementX: true,
        lockMovementY: true,
        lockRotation: true,
        lockScalingX: true,
        lockScalingY: true,
        lockSkewingX: true,
        lockSkewingY: true,
        opacity: 0.8,
        name: 'styleLayer',
        excludeFromExport: false,
        evented: false
      });
      
      img.scaleToWidth(window.canvas.width);
      if (img.getScaledHeight() < window.canvas.height) {
        img.scaleToHeight(window.canvas.height);
      }
      
      window.styleLayer = img;
      window.canvas.add(img);
      
      // 确保正确的图层顺序
      if (window.backgroundImage) {
        window.canvas.sendToBack(window.backgroundImage);
      }
      window.canvas.bringForward(img);
      
      // 确保所有文本保持在顶层
      window.canvas.getObjects().forEach(obj => {
        if (obj.textLayer) {
          window.canvas.bringToFront(obj);
        }
      });
      
      window.canvas.requestRenderAll();
    }, function(error) {
      console.error('Failed to load style layer:', error);
      console.log('Make sure the image exists at:', url);
    });
  };

  const initializeTextElements = () => {
    const colors = {
      mainTitle: '#4A90E2',
      subtitle: '#50B794',
      location: '#9B6EDD',
      eventDetails: '#FF9666',
      venueInfo: '#5EADF0'
    };
    
    // Scale factor for positioning (0.8 = 20% reduction)
    const scale = 0.8;
    
    // Main Title
    const mainTitle = new window.fabric.Text('WILD HARMONIES', {
      left: window.canvas.width / 2,
      top: 520 * scale,
      fontSize: 70 * scale,
      fontFamily: 'Arial',
      fill: colors.mainTitle,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'mainTitle'
    });
    window.canvas.add(mainTitle);

    // Subtitle
    const subtitle = new window.fabric.Text('A FULL NIGHT OF ROCK, LIVE IN', {
      left: window.canvas.width / 2,
      top: 620 * scale,
      fontSize: 32 * scale,
      fontFamily: 'Arial',
      fill: colors.subtitle,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'subtitle'
    });
    window.canvas.add(subtitle);

    // Location
    const location = new window.fabric.Text('OTTAWA', {
      left: window.canvas.width / 2,
      top: 670 * scale,
      fontSize: 50 * scale,
      fontFamily: 'Arial',
      fill: colors.location,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'location'
    });
    window.canvas.add(location);

    // Event Details
    const eventDate = new window.fabric.Text('SEP 25, 2025', {
      left: window.canvas.width / 2 - (150 * scale),
      top: 750 * scale,
      fontSize: 22 * scale,
      fontFamily: 'Arial',
      fill: colors.eventDetails,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'eventDate'
    });

    const eventTime = new window.fabric.Text('9:00 PM', {
      left: window.canvas.width / 2,
      top: 750 * scale,
      fontSize: 22 * scale,
      fontFamily: 'Arial',
      fill: colors.eventDetails,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'eventTime'
    });

    const eventPrice = new window.fabric.Text('$23', {
      left: window.canvas.width / 2 + (150 * scale),
      top: 750 * scale,
      fontSize: 22 * scale,
      fontFamily: 'Arial',
      fill: colors.eventDetails,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'eventPrice'
    });

    window.canvas.add(eventDate);
    window.canvas.add(eventTime);
    window.canvas.add(eventPrice);

    // Venue Info
    const venueName = new window.fabric.Text('HOUSE OF TARG', {
      left: window.canvas.width / 2,
      top: 840 * scale,
      fontSize: 18 * scale,
      fontFamily: 'Arial',
      fill: colors.venueInfo,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'venueName'
    });

    const venueAddress = new window.fabric.Text('1077 BANK ST, OTTAWA', {
      left: window.canvas.width / 2,
      top: 865 * scale,
      fontSize: 18 * scale,
      fontFamily: 'Arial',
      fill: colors.venueInfo,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'venueAddress'
    });

    const venueWebsite = new window.fabric.Text('HOUSEOFTARG.COM', {
      left: window.canvas.width / 2,
      top: 890 * scale,
      fontSize: 18 * scale,
      fontFamily: 'Arial',
      fill: colors.venueInfo,
      originX: 'center',
      originY: 'top',
      textAlign: 'center',
      textLayer: true,
      name: 'venueWebsite'
    });

    window.canvas.add(venueName);
    window.canvas.add(venueAddress);
    window.canvas.add(venueWebsite);

    window.canvas.renderAll();
  };

  const setupLayerManagement = () => {
    // Implementation for layer management
  };

  const setupSelectionHandlers = () => {
    // Implementation for selection handlers
  };

  const setupControlHandlers = () => {
    // Set up all the form control handlers
    setTimeout(() => {
      // Text input handlers
      const textInputs = document.querySelectorAll('input[type="text"]:not([data-type="color"])');
      textInputs.forEach(input => {
        input.addEventListener('input', function() {
          updateTextContent(this.id, this.value);
        });
      });

      // Color picker handlers
      if (window.$ && window.$.fn.spectrum) {
        window.$('input[data-type="color"]').spectrum({
          type: "color",
          showInput: true,
          showInitial: true,
          showAlpha: false,
          showButtons: false,
          move: function(color) {
            const id = this.id;
            const textId = id.replace('Color', '');
            updateTextColor(textId, color.toHexString());
          }
        });
      }

      // Font and size handlers
      document.querySelectorAll('.font-select').forEach(select => {
        select.addEventListener('change', function() {
          const textId = this.id.replace('Font', '');
          updateTextFont(textId, this.value);
        });
      });

      document.querySelectorAll('.size-slider').forEach(slider => {
        slider.addEventListener('input', function() {
          const textId = this.id.replace('Size', '');
          updateTextSize(textId, parseInt(this.value));
          document.getElementById(textId + 'SizeValue').textContent = this.value;
        });
      });

      // Image upload handler
      const imageInput = document.getElementById('imageInput');
      if (imageInput) {
        imageInput.addEventListener('change', handleImageUpload);
      }

      // Button handlers
      document.getElementById('deleteSelected')?.addEventListener('click', deleteSelectedObjects);
      document.getElementById('resetButton')?.addEventListener('click', resetPoster);
      document.getElementById('cropButton')?.addEventListener('click', applyCrop);
      document.getElementById('cancelCrop')?.addEventListener('click', cancelCrop);

      // Style layer controls
      const styleLayerOpacity = document.getElementById('styleLayerOpacity');
      const toggleStyleLayer = document.getElementById('toggleStyleLayer');
      
      if (styleLayerOpacity) {
        styleLayerOpacity.addEventListener('input', function() {
          const opacity = parseInt(this.value) / 100;
          document.getElementById('styleLayerOpacityValue').textContent = this.value;
          
          if (window.styleLayer) {
            window.styleLayer.set('opacity', opacity);
            window.canvas.requestRenderAll();
          }
        });
      }

      if (toggleStyleLayer) {
        toggleStyleLayer.addEventListener('click', function() {
          if (window.styleLayer) {
            const isVisible = window.styleLayer.visible !== false;
            window.styleLayer.set('visible', !isVisible);
            window.canvas.requestRenderAll();
            this.textContent = isVisible ? 'Show Style Layer' : 'Hide Style Layer';
          } else {
            // 如果样式层还没加载，重新加载它
            loadStyleLayer('/poster-images/Poster2-StyleLayer.png');
          }
        });
      }
    }, 1000);
  };

  const updateTextContent = (textId, value) => {
    if (window.canvas) {
      const textObj = window.canvas.getObjects().find(obj => obj.name === textId);
      if (textObj) {
        textObj.set('text', value);
        window.canvas.requestRenderAll();
      }
    }
  };

  const updateTextColor = (textId, color) => {
    if (window.canvas) {
      const textObj = window.canvas.getObjects().find(obj => obj.name === textId);
      if (textObj) {
        textObj.set('fill', color);
        window.canvas.requestRenderAll();
      }
    }
  };

  const updateTextFont = (textId, fontFamily) => {
    if (window.canvas) {
      const textObj = window.canvas.getObjects().find(obj => obj.name === textId);
      if (textObj) {
        textObj.set('fontFamily', fontFamily);
        window.canvas.requestRenderAll();
      }
    }
  };

  const updateTextSize = (textId, fontSize) => {
    if (window.canvas) {
      const textObj = window.canvas.getObjects().find(obj => obj.name === textId);
      if (textObj) {
        textObj.set('fontSize', fontSize);
        window.canvas.requestRenderAll();
      }
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(event) {
      const modal = document.getElementById('cropModal');
      const cropImage = document.getElementById('cropImage');
      
      modal.style.display = 'block';
      cropImage.src = event.target.result;
      
      if (window.cropper) {
        window.cropper.destroy();
      }
      
      window.cropper = new window.Cropper(cropImage, {
        aspectRatio: window.canvas.width / (window.canvas.height * 0.56),
        viewMode: 1,
        dragMode: 'move',
        autoCropArea: 1,
        restore: false,
        modal: true,
        guides: true,
        highlight: true,
        cropBoxMovable: true,
        cropBoxResizable: true,
        toggleDragModeOnDblclick: true
      });
    };
    reader.readAsDataURL(file);
  };

  const applyCrop = () => {
    if (!window.cropper) return;

    const croppedCanvas = window.cropper.getCroppedCanvas({
      width: window.canvas.width * 0.56,
      height: window.canvas.height * 0.56
    });

    window.fabric.Image.fromURL(croppedCanvas.toDataURL(), function(img) {
      img.set({
        left: 0,
        top: 0
      });
      img.scaleToWidth(window.canvas.width);
      window.canvas.add(img);
      window.canvas.requestRenderAll();
      img.sendToBack();
    });

    document.getElementById('cropModal').style.display = 'none';
    window.cropper.destroy();
    window.cropper = null;
  };

  const cancelCrop = () => {
    try {
      const cropModal = document.getElementById('cropModal');
      if (cropModal) {
        cropModal.style.display = 'none';
      }

      if (window.cropper && typeof window.cropper.destroy === 'function') {
        window.cropper.destroy();
        window.cropper = null;
      }
    } catch (error) {
      console.error('Error canceling crop:', error);
    }
  };

  const resetPoster = () => {
    if (!window.canvas) return;

    try {
      window.canvas.clear();

      // Clean up global references
      window.backgroundImage = null;
      window.styleLayer = null;

      // 重置样式层控件
      const styleLayerOpacity = document.getElementById('styleLayerOpacity');
      const styleLayerOpacityValue = document.getElementById('styleLayerOpacityValue');
      const toggleStyleLayer = document.getElementById('toggleStyleLayer');

      if (styleLayerOpacity) styleLayerOpacity.value = 80;
      if (styleLayerOpacityValue) styleLayerOpacityValue.textContent = '80';
      if (toggleStyleLayer) toggleStyleLayer.textContent = 'Toggle Style Layer';

      // 重新初始化poster
      setTimeout(() => {
        initializePoster();
      }, 100);
    } catch (error) {
      console.error('Error resetting poster:', error);
    }
  };

  const downloadPoster = () => {
    if (!window.canvas) {
      console.warn('Canvas not available for download');
      return;
    }

    try {
      const dataURL = window.canvas.toDataURL({
        format: 'png',
        quality: 1
      });

      const link = document.createElement('a');
      link.download = 'poster.png';
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading poster:', error);
      alert('Failed to download poster. Please try again.');
    }
  };

  return (
    <>
      <Box className="!fixed lg:!block !hidden !z-30 !left-0 !right-0 !top-0">
        <Navbar />
      </Box>
      <Box className="!flex">
        <Box className="!hidden lg:!block">
          <Sidebar />
        </Box>
        <Box className="!inline lg:!hidden">
          <MobileNavbar />
        </Box>
        <Box className="lg:!pl-32 !pl-0 lg:!pt-24 !pt-5 !w-full">
          <style jsx>{`
            .main-container {
              display: flex;
              flex-direction: row;
              min-height: 100vh;
              width: 100%;
              padding: 30px;
              gap: 30px;
              background: var(--bg-color);
            }

            .poster-container {
              flex: 1;
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              justify-content: center;
              min-width: 0;
              gap: 20px;
              position: relative;
            }

            #poster {
              width: 518px;
              height: 720px;
              background-color: white;
              position: relative;
              transform-origin: top center;
              max-width: 100%;
              max-height: 90vh;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
              margin: 0 auto;
            }

            #posterCanvas {
              width: 100%;
              height: 100%;
              background-color: white;
            }

            .control-panel {
              width: 420px;
              background: var(--footer-bg);
              padding: 30px;
              border-radius: 20px;
              box-shadow: 0 4px 30px rgba(0, 0, 0, 0.3);
              border: 1px solid var(--divider-color);
              height: auto;
              max-height: calc(100vh - 60px);
              overflow-y: auto;
              position: sticky;
              top: 30px;
              flex-shrink: 0;
            }

            .control-group {
              margin-bottom: 30px;
              padding: 25px;
              border-radius: 16px;
              background: var(--bg-color);
              border: 1px solid var(--light-border-color);
              transition: all 0.3s ease;
            }

            .control-group:hover {
              box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
              border-color: var(--divider-color);
            }

            .control-label {
              font-weight: 600;
              font-size: 16px;
              color: var(--text-color);
              margin-bottom: 15px;
              display: block;
              font-family: var(--craftWorkMedium);
            }

            .poster-toolbar {
              display: flex;
              flex-direction: column;
              gap: 20px;
              padding: 15px;
              background: var(--footer-bg);
              border-radius: 12px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
              border: 1px solid var(--light-border-color);
            }

            .toolbar-group {
              display: flex;
              flex-direction: column;
              gap: 10px;
              padding-bottom: 20px;
              border-bottom: 1px solid var(--light-border-color);
            }

            .toolbar-group:last-child {
              padding-bottom: 0;
              border-bottom: none;
            }

            .toolbar-btn {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding: 12px;
              border: 1px solid var(--light-border-color);
              border-radius: 8px;
              background: var(--bg-color);
              color: var(--text-color);
              cursor: pointer;
              transition: all 0.2s ease;
              width: 80px;
              gap: 8px;
              font-family: var(--craftWorkRegular);
            }

            .toolbar-btn:hover {
              background: var(--light-border-color);
              transform: translateY(-1px);
              border-color: var(--divider-color);
            }

            .toolbar-btn i {
              font-size: 20px;
            }

            .toolbar-btn span {
              font-size: 12px;
              font-weight: 500;
            }

            .toolbar-btn.download-btn {
              background: var(--inprogress-color);
              color: white;
              border-color: var(--inprogress-color);
            }

            .toolbar-btn.download-btn:hover {
              background: var(--switch-border);
              border-color: var(--switch-border);
            }

            .toolbar-btn.share-btn.facebook {
              background: #1877f2;
              color: white;
            }

            .toolbar-btn.share-btn.facebook:hover {
              background: #0d6efd;
            }

            .toolbar-btn.share-btn.instagram {
              background: #e4405f;
              background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
              color: white;
            }

            .toolbar-btn.share-btn.instagram:hover {
              opacity: 0.9;
            }

            #deleteSelected {
              background: var(--delete-color);
              color: white;
              border-color: var(--delete-color);
            }

            #deleteSelected:hover {
              background: var(--warn-color);
              border-color: var(--warn-color);
            }

            .color-row {
              display: flex;
              gap: 10px;
              align-items: center;
              margin-bottom: 15px;
            }

            .font-color-controls {
              display: flex;
              gap: 10px;
              align-items: center;
            }

            .font-size-control {
              margin-top: 15px;
            }

            .font-size-control label {
              color: var(--text-color);
              font-family: var(--craftWorkRegular);
              font-size: 14px;
            }

            .size-slider {
              width: 100%;
              margin-top: 8px;
              accent-color: var(--inprogress-color);
            }

            .form-control {
              flex: 1;
              padding: 10px 15px;
              border: 1px solid var(--light-border-color);
              border-radius: 8px;
              font-size: 14px;
              background: var(--bg-color);
              color: var(--text-color);
              font-family: var(--craftWorkRegular);
              transition: border-color 0.2s ease;
            }

            .form-control:focus {
              outline: none;
              border-color: var(--inprogress-color);
              box-shadow: 0 0 0 3px rgba(127, 124, 220, 0.2);
            }

            .image-upload-container {
              border: 2px dashed var(--light-border-color);
              border-radius: 12px;
              padding: 30px;
              text-align: center;
              transition: all 0.3s ease;
              background: var(--footer-bg);
            }

            .image-upload-container:hover {
              border-color: var(--inprogress-color);
              background: var(--bg-color);
            }

            .upload-label {
              display: inline-flex;
              flex-direction: column;
              align-items: center;
              gap: 10px;
              cursor: pointer;
              color: var(--inprogress-color);
              font-weight: 500;
              font-family: var(--craftWorkMedium);
            }

            .upload-label i {
              font-size: 24px;
            }

            #imageInput {
              display: none;
            }

            .crop-modal {
              display: none;
              position: fixed;
              z-index: 1000;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background-color: rgba(0,0,0,0.8);
            }

            .crop-modal-content {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: var(--footer-bg);
              border: 1px solid var(--light-border-color);
              padding: 30px;
              border-radius: 20px;
              max-width: 90vw;
              max-height: 90vh;
              box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            }

            .crop-container {
              margin-bottom: 20px;
              max-height: 70vh;
              overflow: hidden;
            }

            .crop-controls {
              display: flex;
              gap: 15px;
              justify-content: center;
            }

            .crop-button {
              padding: 12px 24px;
              border: 1px solid var(--light-border-color);
              border-radius: 8px;
              cursor: pointer;
              font-weight: 500;
              transition: all 0.2s ease;
              background: var(--bg-color);
              color: var(--text-color);
              font-family: var(--craftWorkRegular);
            }

            .crop-button:hover {
              background: var(--light-border-color);
              border-color: var(--divider-color);
            }

            .btn {
              padding: 12px 24px;
              border: 1px solid var(--inprogress-color);
              border-radius: 8px;
              cursor: pointer;
              font-weight: 500;
              transition: all 0.2s ease;
              background: var(--inprogress-color);
              color: white;
              font-family: var(--craftWorkMedium);
            }

            .btn:hover {
              background: var(--switch-border);
              border-color: var(--switch-border);
            }

            .three-columns {
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              gap: 10px;
              margin-bottom: 15px;
            }

            .input-full {
              grid-column: 1 / -1;
              display: flex;
              gap: 10px;
              margin-top: 10px;
            }
          `}</style>
          
          <div className="main-container">
            <div className="poster-container">
              <div className="poster-toolbar">
                <div className="toolbar-group">
                  <button className="toolbar-btn" id="deleteSelected" title="Delete Selected">
                    <i className="fas fa-trash"></i>
                    <span>Delete</span>
                  </button>
                  <button className="toolbar-btn" id="bringForward" title="Bring Forward">
                    <i className="fas fa-arrow-up"></i>
                    <span>Bring Forward</span>
                  </button>
                  <button className="toolbar-btn" id="sendBackward" title="Send Backward">
                    <i className="fas fa-arrow-down"></i>
                    <span>Send Backward</span>
                  </button>
                  <button className="toolbar-btn" id="resizeButton" title="Resize Poster">
                    <i className="fas fa-expand"></i>
                    <span>Resize</span>
                  </button>
                </div>
                <div className="toolbar-group">
                  <button className="toolbar-btn download-btn" onClick={downloadPoster} title="Download Poster">
                    <i className="fas fa-cloud-download-alt"></i>
                    <span>Download</span>
                  </button>
                </div>
                <div className="toolbar-group">
                  <button className="toolbar-btn share-btn facebook" title="Share to Facebook">
                    <i className="fab fa-facebook-f"></i>
                    <span>Facebook</span>
                  </button>
                  <button className="toolbar-btn share-btn instagram" title="Share to Instagram">
                    <i className="fab fa-instagram"></i>
                    <span>Instagram</span>
                  </button>
                </div>
              </div>
              <div id="poster">
                <canvas ref={canvasRef} id="posterCanvas" width="518" height="720"></canvas>
              </div>
            </div>

            <div className="control-panel">
              <h4 className="mb-4" style={{color: 'var(--text-color)', fontFamily: 'var(--craftWorkSemiBold)'}}>Poster Controls</h4>
              
              <div className="control-group">
                <label className="control-label">Background Image</label>
                <div className="image-upload-container">
                  <label className="upload-label" htmlFor="imageInput">
                    <i className="fas fa-cloud-upload-alt"></i>
                    Choose Image
                  </label>
                  <input type="file" id="imageInput" accept="image/*" />
                  <div className="preview-container"></div>
                </div>
              </div>

              <div className="control-group">
                <label className="control-label">Style Layer (Poster2-StyleLayer.png)</label>
                <div className="font-size-control">
                  <label>Opacity: <span id="styleLayerOpacityValue">80</span>%</label>
                  <input type="range" id="styleLayerOpacity" min="0" max="100" defaultValue="80" className="size-slider" />
                </div>
                <button id="toggleStyleLayer" className="btn" style={{marginTop: '10px', fontSize: '14px', padding: '8px 16px'}}>
                  Toggle Style Layer
                </button>
              </div>

              <div className="control-group">
                <label className="control-label">Main Title</label>
                <div className="color-row">
                  <input type="text" className="form-control" id="mainTitle" defaultValue="WILD HARMONIES" data-target="mainTitle" />
                  <div className="font-color-controls">
                    <select className="form-control font-select" id="mainTitleFont" data-target="mainTitle">
                      <option value="Arial">Arial</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Impact">Impact</option>
                    </select>
                    <input type="text" className="form-control" id="mainTitleColor" data-type="color" data-target="mainTitle" defaultValue="#4A90E2" />
                  </div>
                </div>
                <div className="font-size-control">
                  <label>Font Size: <span id="mainTitleSizeValue">56</span>px</label>
                  <input type="range" id="mainTitleSize" data-target="mainTitle" min="16" max="96" defaultValue="56" className="size-slider" />
                </div>
              </div>

              <div className="control-group">
                <label className="control-label">Subtitle</label>
                <div className="color-row">
                  <input type="text" className="form-control" id="subtitle" defaultValue="A FULL NIGHT OF ROCK, LIVE IN" />
                  <div className="font-color-controls">
                    <select className="form-control font-select" id="subtitleFont">
                      <option value="Arial">Arial</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Impact">Impact</option>
                    </select>
                    <input type="text" className="form-control" id="subtitleColor" data-type="color" defaultValue="#4A90E2" />
                  </div>
                </div>
                <div className="font-size-control">
                  <label>Font Size: <span id="subtitleSizeValue">26</span>px</label>
                  <input type="range" id="subtitleSize" min="10" max="48" defaultValue="26" className="size-slider" />
                </div>
              </div>

              <div className="control-group">
                <label className="control-label">Location</label>
                <div className="color-row">
                  <input type="text" className="form-control" id="location" defaultValue="OTTAWA" />
                  <div className="font-color-controls">
                    <select className="form-control font-select" id="locationFont">
                      <option value="Arial">Arial</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Impact">Impact</option>
                    </select>
                    <input type="text" className="form-control" id="locationColor" data-type="color" defaultValue="#4A90E2" />
                  </div>
                </div>
                <div className="font-size-control">
                  <label>Font Size: <span id="locationSizeValue">40</span>px</label>
                  <input type="range" id="locationSize" min="16" max="64" defaultValue="40" className="size-slider" />
                </div>
              </div>

              <div className="control-group">
                <label className="control-label">Event Details</label>
                <div className="input-group three-columns">
                  <div>
                    <input type="text" className="form-control" id="eventDate" defaultValue="SEP 25, 2025" />
                  </div>
                  <div>
                    <input type="text" className="form-control" id="eventTime" defaultValue="9:00 PM" />
                  </div>
                  <div>
                    <input type="text" className="form-control" id="eventPrice" defaultValue="$23" />
                  </div>
                  <div className="input-full">
                    <select className="form-control font-select" id="eventDetailsFont">
                      <option value="Arial">Arial</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Impact">Impact</option>
                    </select>
                    <input type="text" className="form-control" id="eventDetailsColor" data-type="color" defaultValue="#4A90E2" />
                  </div>
                </div>
                <div className="font-size-control">
                  <label>Font Size: <span id="eventDetailsSizeValue">18</span>px</label>
                  <input type="range" id="eventDetailsSize" min="10" max="32" defaultValue="18" className="size-slider" />
                </div>
              </div>

              <div className="control-group">
                <label className="control-label">Venue Information</label>
                <div className="color-row">
                  <input type="text" className="form-control" id="venueName" defaultValue="HOUSE OF TARG" />
                  <div className="font-color-controls">
                    <select className="form-control font-select" id="venueInfoFont">
                      <option value="Arial">Arial</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                      <option value="Impact">Impact</option>
                    </select>
                    <input type="text" className="form-control" id="venueInfoColor" data-type="color" defaultValue="#4A90E2" />
                  </div>
                </div>
                <input type="text" className="form-control" id="venueAddress" defaultValue="1077 BANK ST, OTTAWA" />
                <input type="text" className="form-control" id="venueWebsite" defaultValue="HOUSEOFTARG.COM" />
                <div className="font-size-control">
                  <label>Font Size: <span id="venueInfoSizeValue">14</span>px</label>
                  <input type="range" id="venueInfoSize" min="8" max="24" defaultValue="14" className="size-slider" />
                </div>
              </div>

              <div className="control-group">
                <button id="resetButton" className="btn">Reset All Changes</button>
              </div>
            </div>
          </div>

          {/* Crop Modal */}
          <div id="cropModal" className="crop-modal">
            <div className="crop-modal-content">
              <span className="close-modal">&times;</span>
              <div className="crop-container">
                <img id="cropImage" src="" alt="Crop" />
              </div>
              <div className="crop-controls">
                <button className="crop-button" id="rotateLeft">↺ Rotate Left</button>
                <button className="crop-button" id="rotateRight">↻ Rotate Right</button>
                <button className="crop-button" id="cropButton">Apply Crop</button>
                <button className="crop-button" id="cancelCrop">Cancel</button>
              </div>
            </div>
          </div>
        </Box>
      </Box>
    </>
  );
};

export default PosterPage;