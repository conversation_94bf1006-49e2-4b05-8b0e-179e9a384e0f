@import url(../assets/fonts/all.fonts.css);
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --text-color: #efefef;
  --bg-color: #181b1b;
  --footer-bg: #202222;
  --hide-color: #7a7d8b;
  --inprogress-color: #7f7cdc;
  --image-bg: #181b1b1a;
  --calendar-text-color: #646771;
  --slick-active: #333333;
  --slick-dots: #d9d9d9;
  --disabled-color: #efefef1a;
  --profile-type: #202222b2;
  --light-text: #25130073;
  --switch-border: #5d5aba;
  --light-border-color: #393c3c;
  --done-color: #e5ac5b;
  --confirmed-color: #619c74;
  /* --available-color: #272929; */
  /* --available-bg:#272929; */
  --booked-color: #313333;
  --declined-color: #e07f54;
  --warn-color: #E05454;
  --delete-color: #e05454;
  --discount-color: #e07f5433;
  --divider-color: rgba(76, 78, 79, 0.5);
  --craftWorkRegular: "CraftworkGroteskRegular";
  --craftWorkGX: "CraftworkGroteskGX";
  --craftWorkMedium: "CraftworkGroteskMedium";
  --craftWorkSemiBold: "CraftworkGroteskSemiBold";
  --craftWorkBold: "CraftworkGroteskBold";
  --craftWorkHeavy: "CraftworkGroteskHeavy";
  --sora--thin: "Sora100";
  --sora-extralight: "Sora200";
  --sora-light: "Sora300";
  --sora-regular: "Sora400";
  --sora-medium: "Sora500";
  --sora-semibold: "Sora600";
  --sora-bold: "Sora700";
  --sora-extrabold: "Sora800";
  --pop-thin: "Poppins100";
  --pop-extraLight: "Poppins200";
  --pop-light: "Poppins300";
  --pop-regular: "Poppins400";
  --pop-medium: "Poppins500";
  --pop-semibold: "Poppins600";
  --pop-bold: "Poppins700";
  --pop-extraBold: "Poppins800";
  --pop-black: "Poppins900";
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color) !important;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

.alertIcon div svg {
  color: white !important;
  margin-top: 2px !important;
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--bg-color) inset;
  -webkit-text-fill-color: var(--text-color);
}

.act-info-card-slider {
  width: 200px;
  height: 200px;
}

.Mui-disabled {
  -webkit-text-fill-color: var(--hide-color) !important;
}

.MuiOutlinedInput-input:-webkit-autofill {
  border-radius: 0 !important;
}

*::-webkit-scrollbar-track {
  border-radius: 5px;
  color: var(--arrow-icon);
}

*::-webkit-scrollbar-thumb {
  background-color: var(--border-light);
  border-radius: 14px;
}

/* Sidebar responsive layout utilities */
.sidebar-content {
  margin-left: 12px;
  transition: margin-left 0.3s ease;
}

@media (min-width: 1024px) {
  .sidebar-content {
    margin-left: 100px;
  }

  .sidebar-content.sidebar-expanded {
    margin-left: 300px;
  }
}

*::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.slider-home .slick-dots li button:before {
  color: var(--slick-dots) !important;
  opacity: 0.99 !important;
}

.slider-home .slick-dots li.slick-active button:before {
  color: var(--slick-active) !important;
  opacity: 0.99 !important;
}

.slider-class ul {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 10px;
}

.slider-class .slick-dots li button:before {
  border: 1px solid var(--text-color);
  color: transparent;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  opacity: 0.99;
}

.slider-class .slick-dots li.slick-active button:before {
  color: transparent;
  background-color: white;
}

.dashboard-slider .slick-slide div {
  margin-right: 20px;
}

.slider-class .slick-next {
  color: var(--text-color);
}

.slider-class .slick-next:hover {
  color: var(--text-color);
}

.slider-class .slick-prev {
  color: var(--text-color);
}

.slider-class .slick-prev:hover {
  color: var(--text-color);
}

.slick-slider .slick-prev {
  left: 30px;
  z-index: 10;
}

.slick-slider .slick-next {
  right: 30px;
  z-index: 10;
}

.slick-vertical .slick-slide {
  outline: none;
}

.slick-vertical .slick-slide .slide-list {
  width: 18vw;
}

.favourites-slider .slick-next {
  color: var(--text-color);
}

.favourites-slider ul {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 10px;
}

.favourites-slider .slick-dots li button:before {
  border: 1px solid var(--text-color);
  color: transparent;
  width: 7px;
  height: 7px;
  border-radius: 1px;
  opacity: 0.99;
}

.favourites-slider .slick-dots li.slick-active button:before {
  color: transparent;
  background-color: white;
}

.favourites-slider .slick-next {
  color: var(--text-color);
}

.favourites-slider .slick-next:hover {
  color: var(--text-color);
}

.favourites-slider .slick-prev {
  color: var(--text-color);
}

.favourites-slider .slick-prev:hover {
  color: var(--text-color);
}

.profileCard-slider .slick-next {
  color: var(--text-color);
}

.profileCard-slider .slick-next:hover {
  color: var(--text-color);
}

.profileCard-slider .slick-prev {
  color: var(--text-color);
}

.profileCard-slider .slick-prev:hover {
  color: var(--text-color);
}

.slider-container .slick-list {
  width: 78vw;
}

.event-slider .slick-list {
  width: 70vw;
}

.event-slider .slick-slider .slick-next {
  right: 45%;
}

/* CustomSlider.css */
.slider-container {
  display: flex !important;
  align-items: flex-start !important;
}

.custom-paging {
  margin-right: 16px !important;
}

.custom-paging ul {
  list-style-type: none !important;
  padding: 0 !important;
}

.custom-paging li {
  cursor: pointer !important;
  margin-bottom: 10px !important;
  border-radius: 4px !important;
}

.custom-paging li.active {
  background-color: #ddd !important;
  font-weight: bold !important;
}

.slick-act-slides {
  text-align: center !important;
}

.slick-act-slides img {
  width: 98% !important;
  height: 530px !important;
}

@media screen and (max-width: 1228px) {
  .slick-act-slides img {
    height: 360px !important;
  }
}

.slick-act-slides .slick-arrow {
  color: var(--bg-color);
  background-color: var(--text-color);
  border-radius: 50%;
  font-size: 28px;
}

.slick-act-slides .slick-arrow:hover {
  color: var(--bg-color);
  background-color: var(--text-color);
  border-radius: 50%;
  font-size: 28px;
}

.slick-thumb-act-detail .slick-arrow {
  color: var(--text-color);
}

.slick-thumb-act-detail .slick-arrow:hover {
  color: var(--text-color);
}

.slick-thumb-act-detail .slick-prev {
  left: 91%;
  top: -20px;
}

.slick-thumb-act-detail .slick-prev:hover {
  left: 91%;
  top: -20px;
}

.slick-thumb-act-detail .slick-next {
  left: 96%;
  top: -20px;
}

.slick-thumb-act-detail .slick-next:hover {
  left: 96%;
  top: -20px;
}

.vertical-slider {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  height: 530px;
  display: flex;
  flex-direction: column;
}

@media screen and (max-width: 1228px) {
  .vertical-slider {
    height: 320px !important;
  }
}

.vertical-slider li {
  margin-bottom: 10px;
  cursor: pointer;
}

.vertical-slider li img {
  width: 120px;
  height: 120px;
}

@media screen and (max-width: 508px) {
  .vertical-slider li img {
    width: 80px;
    height: 100px;
  }
}

@media screen and (max-width: 508px) {
  .slider-container .slick-list {
    width: 75vw;
  }
}

.react-datepicker__input-container .react-date-picker-class {
  height: 56px;
  border-radius: 2px;
  border: 1px solid var(--text-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  width: 100%;
  padding-left: 10px;
}

.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker-wrapper>input:focus-visible {
  outline: none;
}

.react-datepicker-ignore-onclickoutside {
  outline: none;
}

.react-date-picker {
  border: none !important;
  border-radius: 50%;
}

.react-date-picker:focus-visible {
  outline: none !important;
}

.react-datepicker__input-container .react-date-picker {
  height: 56px;
  border-radius: 50%;
  border: 1px solid var(--text-color);
  background-color: var(--bg-color);
  color: var(--text-color);
  /* width: 100%; */
  padding-left: 10px;
}

.menu .MuiPaper-root {
  top: 55px !important;
  left: 32vw !important;
}

@media screen and (min-width: 1024px) and (max-width: 1245px) {
  .menu .MuiPaper-root {
    top: 55px !important;
    left: 46vw !important;
  }
}

@media screen and (max-width: 1023px) and (min-width: 446px) {
  .menu .MuiPaper-root {
    top: 120px !important;
    left: 80vw !important;
  }
}

@media screen and (max-width: 445px) {
  .menu .MuiPaper-root {
    top: 120px !important;
    left: 55vw !important;
  }
}

.slide-classes .slick-list .slick-track .slick-slide {
  margin-right: 15px;
}

/* .slide-classes .slick-list {
  height: 750px !important;
} */
/* Ensure that all event boxes in the month view have a fixed height */
.fc-daygrid-event {
  height: 80px !important;
  /* Matches our JSX height */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Prevent text overflow in events */
.fc-event-title-container {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.fc-day-today {
  background-color: #202222 !important;
  /* Match your calendar background */
  color: white !important;
  /* Ensure text remains readable */
  border: none !important;
  /* Remove default border */
}

@media screen and (max-width: 445px) {
  .slide-classes .slick-list .slick-track .slick-slide {
    margin-right: 10px;
  }
}

.event-tooltip {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}